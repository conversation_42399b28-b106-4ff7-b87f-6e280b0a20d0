#include "iq_video_stream_processor.h"
#include <stdexcept>
#include <iostream>
#include <sstream>
#include <iomanip>
#include <chrono>
#include <algorithm>
#include <node.h>
#include <v8.h>

IQVideoStreamProcessor::IQVideoStreamProcessor(std::unique_ptr<IIQStream> stream, const VideoProcessingConfig& config, const FrameCallback& frameCallback)
    : config_(config)
    , frameCallback_(frameCallback)
    , stream_(std::move(stream))
    , running_(false)
    , stopRequested_(false) {
}

IQVideoStreamProcessor::~IQVideoStreamProcessor() {
    stop();
}

bool IQVideoStreamProcessor::start() {
    if (running_) {
        setError("IQ video stream processor is already running");
        return false;
    }
    
    try {
        // Validate configuration
        if (!validateConfig(config_)) {
            return false;
        }
        
        // Validate stream
        if (!stream_ || !stream_->isActive()) {
            setError("IQ stream is not active: " + (stream_ ? stream_->lastError() : "null stream"));
            return false;
        }
        
        // Calculate worker configuration using stream sample rate
        auto workerConfig = config_.toWorkerConfig();
        workerConfig.sampleRate = stream_->sampleRate();  // Use actual stream sample rate

        // Create acquisition worker
        acquisitionWorker_ = std::make_unique<AcquisitionWorker<uint32_t>>(
            std::move(stream_),
            workerConfig,
            [this](uint32_t* data, size_t size) { handleProcessedChunk(data, size); }
        );
        
        // Start the acquisition worker
        if (!acquisitionWorker_->start()) {
            setError("Failed to start acquisition worker: " + acquisitionWorker_->lastError());
            return false;
        }
        
        running_ = true;
        return true;
        
    } catch (const std::exception& e) {
        setError("Exception starting IQ video stream processor: " + std::string(e.what()));
        return false;
    }
}

void IQVideoStreamProcessor::stop() {
    if (running_.exchange(false)) {
        stopRequested_ = true;
        
        if (acquisitionWorker_) {
            acquisitionWorker_->requestStop();
            acquisitionWorker_->join();
            acquisitionWorker_.reset();
        }
        
        // Note: stream_ is now owned by acquisitionWorker_, so it's cleaned up there
        stream_.reset();
    }
}

bool IQVideoStreamProcessor::isRunning() const {
    return running_ && acquisitionWorker_ && acquisitionWorker_->isRunning();
}

std::string IQVideoStreamProcessor::getStats() const {
    if (!acquisitionWorker_) {
        return "{}";
    }

    try {
        // Get stats from acquisition worker and format as JSON
        auto stats = acquisitionWorker_->getStats();

        std::ostringstream json;
        json << "{"
             << "\"running\":" << (isRunning() ? "true" : "false") << ","
             << "\"totalSamplesRead\":" << stats.totalSamplesRead << ","
             << "\"totalChunksProcessed\":" << stats.totalChunksProcessed << ","
             << "\"streamErrors\":" << stats.streamErrors << ","
             << "\"averageReadRate\":" << std::fixed << std::setprecision(2) << stats.averageReadRate
             << "}";

        return json.str();

    } catch (const std::exception& e) {
        return "{\"error\":\"" + std::string(e.what()) + "\"}";
    }
}

const std::string& IQVideoStreamProcessor::lastError() const {
    return lastError_;
}

bool IQVideoStreamProcessor::validateConfig(const VideoProcessingConfig& config, std::string& errorMsg) {
    if (config.frameWidthPx == 0) {
        errorMsg = "Frame width cannot be zero";
        return false;
    }
    
    if (config.frameWidthPx > 4096) {
        errorMsg = "Frame width too large (max 4096 pixels)";
        return false;
    }
    
    if (config.queueDepth.raw == 0 || 
        config.queueDepth.demod == 0 || 
        config.queueDepth.lines == 0) {
        errorMsg = "Queue depths must be positive";
        return false;
    }
    
    if (config.queueDepth.raw > 1024 || 
        config.queueDepth.demod > 1024 || 
        config.queueDepth.lines > 1024) {
        errorMsg = "Queue depths too large (max 1024 each)";
        return false;
    }
    
    if (config.sliceStrategy.empty()) {
        errorMsg = "Slice strategy cannot be empty";
        return false;
    }
    
    return true;
}

bool IQVideoStreamProcessor::validateConfig(const VideoProcessingConfig& config) {
    std::string errorMsg;
    if (!validateConfig(config, errorMsg)) {
        setError("Configuration validation failed: " + errorMsg);
        return false;
    }
    return true;
}



void IQVideoStreamProcessor::handleProcessedChunk(uint32_t* data, size_t size) {
    // For now, output to stdout as specified in strategy document
    // TODO: Implement actual video processing pipeline (stages 3-6)
    std::cout << "Processed chunk: " << size << " samples" << std::endl;

    // TODO: When video processing is implemented, call frameCallback_ with frame data
    // frameCallback_(frameData, frameSize);
}

void IQVideoStreamProcessor::setError(const std::string& error) {
    lastError_ = error;
}

IQVideoStreamProcessor::VideoProcessingConfig IQVideoStreamProcessor::parseVideoProcessingConfig(v8::Isolate* isolate, v8::Local<v8::Object> processingObj) {
    VideoProcessingConfig config;
    v8::Local<v8::Context> context = isolate->GetCurrentContext();

    // Center offset
    v8::Local<v8::Value> centerOffsetVal = processingObj->Get(context, v8::String::NewFromUtf8(isolate, "centerOffsetHz").ToLocalChecked()).ToLocalChecked();
    if (centerOffsetVal->IsNumber()) {
        config.centerOffsetHz = centerOffsetVal->NumberValue(context).FromJust();
    }

    // Slice strategy
    v8::Local<v8::Value> sliceStrategyVal = processingObj->Get(context, v8::String::NewFromUtf8(isolate, "sliceStrategy").ToLocalChecked()).ToLocalChecked();
    if (sliceStrategyVal->IsString()) {
        v8::String::Utf8Value sliceStrategyStr(isolate, sliceStrategyVal);
        config.sliceStrategy = std::string(*sliceStrategyStr);
    } else {
        config.sliceStrategy = "auto_ntsc";  // default
    }

    // Frame width
    v8::Local<v8::Value> frameWidthVal = processingObj->Get(context, v8::String::NewFromUtf8(isolate, "frameWidthPx").ToLocalChecked()).ToLocalChecked();
    if (frameWidthVal->IsNumber()) {
        config.frameWidthPx = frameWidthVal->Uint32Value(context).FromJust();
    } else {
        config.frameWidthPx = 640;  // default
    }

    // Queue depths
    v8::Local<v8::Value> queueDepthVal = processingObj->Get(context, v8::String::NewFromUtf8(isolate, "queueDepth").ToLocalChecked()).ToLocalChecked();
    if (queueDepthVal->IsObject()) {
        v8::Local<v8::Object> queueDepthObj = queueDepthVal.As<v8::Object>();

        v8::Local<v8::Value> rawVal = queueDepthObj->Get(context, v8::String::NewFromUtf8(isolate, "raw").ToLocalChecked()).ToLocalChecked();
        if (rawVal->IsNumber()) {
            config.queueDepth.raw = rawVal->Uint32Value(context).FromJust();
        } else {
            config.queueDepth.raw = 128;  // default
        }

        v8::Local<v8::Value> demodVal = queueDepthObj->Get(context, v8::String::NewFromUtf8(isolate, "demod").ToLocalChecked()).ToLocalChecked();
        if (demodVal->IsNumber()) {
            config.queueDepth.demod = demodVal->Uint32Value(context).FromJust();
        } else {
            config.queueDepth.demod = 128;  // default
        }

        v8::Local<v8::Value> linesVal = queueDepthObj->Get(context, v8::String::NewFromUtf8(isolate, "lines").ToLocalChecked()).ToLocalChecked();
        if (linesVal->IsNumber()) {
            config.queueDepth.lines = linesVal->Uint32Value(context).FromJust();
        } else {
            config.queueDepth.lines = 64;  // default
        }
    } else {
        // Use defaults
        config.queueDepth.raw = 128;
        config.queueDepth.demod = 128;
        config.queueDepth.lines = 64;
    }

    return config;
}
