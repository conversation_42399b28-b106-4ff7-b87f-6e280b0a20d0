#include "iq_video_stream_processor.h"
#include <stdexcept>
#include <iostream>
#include <sstream>
#include <iomanip>
#include <chrono>
#include <algorithm>


IQVideoStreamProcessor::IQVideoStreamProcessor(std::unique_ptr<IIQStream> stream, const VideoProcessingConfig& config, const FrameCallback& frameCallback)
    : config_(config)
    , frameCallback_(frameCallback)
    , stream_(std::move(stream))
    , workerReady_(false)
    , workerStartupFailed_(false)
    , running_(false)
    , stopRequested_(false) {
}

IQVideoStreamProcessor::~IQVideoStreamProcessor() {
    stop();
}

bool IQVideoStreamProcessor::start() {
    if (running_) {
        setError("IQ video stream processor is already running");
        return false;
    }

    try {
        // Validate configuration
        if (!validateConfig(config_)) {
            return false;
        }

        // Validate stream
        if (!stream_ || !stream_->isActive()) {
            setError("IQ stream is not active: " + (stream_ ? stream_->lastError() : "null stream"));
            return false;
        }

        // Calculate worker configuration using stream sample rate
        auto workerConfig = toWorkerConfig(config_);
        workerConfig.sampleRate = stream_->sampleRate();  // Use actual stream sample rate

        // Create acquisition worker
        acquisitionWorker_ = std::make_unique<AcquisitionWorker>(
            std::move(stream_),
            workerConfig,
            [this](SampleType* data, size_t size) { handleProcessedChunk(data, size); }
        );

        // Reset synchronization variables
        workerReady_ = false;
        workerStartupFailed_ = false;
        stopRequested_ = false;

        // Start worker thread with synchronous startup
        std::unique_lock<std::mutex> lock(startupMutex_);
        workerThread_ = std::make_unique<std::thread>(&IQVideoStreamProcessor::workerThreadFunc, this);

        // Wait for worker thread to be ready or fail
        startupCondition_.wait(lock, [this] { return workerReady_.load() || workerStartupFailed_.load(); });

        if (workerStartupFailed_) {
            // Cleanup on failure
            if (workerThread_ && workerThread_->joinable()) {
                workerThread_->join();
            }
            workerThread_.reset();
            acquisitionWorker_.reset();
            return false;
        }

        running_ = true;
        return true;

    } catch (const std::exception& e) {
        setError("Exception starting IQ video stream processor: " + std::string(e.what()));
        return false;
    }
}

void IQVideoStreamProcessor::stop() {
    if (running_.exchange(false)) {
        stopRequested_ = true;

        // Wait for worker thread to finish
        if (workerThread_ && workerThread_->joinable()) {
            workerThread_->join();
        }
        workerThread_.reset();

        // Clean up acquisition worker
        acquisitionWorker_.reset();

        // Note: stream_ is now owned by acquisitionWorker_, so it's cleaned up there
        stream_.reset();
    }
}

bool IQVideoStreamProcessor::isRunning() const {
    return running_.load();
}

std::string IQVideoStreamProcessor::getStats() const {
    if (!acquisitionWorker_) {
        return "{}";
    }

    try {
        // Get stats from acquisition worker and format as JSON
        auto stats = acquisitionWorker_->getStats();

        std::ostringstream json;
        json << "{"
             << "\"running\":" << (isRunning() ? "true" : "false") << ","
             << "\"totalSamplesRead\":" << stats.totalSamplesRead << ","
             << "\"totalChunksProcessed\":" << stats.totalChunksProcessed << ","
             << "\"streamErrors\":" << stats.streamErrors
             << "}";

        return json.str();

    } catch (const std::exception& e) {
        return "{\"error\":\"" + std::string(e.what()) + "\"}";
    }
}

const std::string& IQVideoStreamProcessor::lastError() const {
    return lastError_;
}

bool IQVideoStreamProcessor::validateConfig(const VideoProcessingConfig& config, std::string& errorMsg) {
    if (config.frameWidthPx == 0) {
        errorMsg = "Frame width cannot be zero";
        return false;
    }
    
    if (config.frameWidthPx > 4096) {
        errorMsg = "Frame width too large (max 4096 pixels)";
        return false;
    }
    
    if (config.queueDepth.raw == 0 || 
        config.queueDepth.demod == 0 || 
        config.queueDepth.lines == 0) {
        errorMsg = "Queue depths must be positive";
        return false;
    }
    
    if (config.queueDepth.raw > 1024 || 
        config.queueDepth.demod > 1024 || 
        config.queueDepth.lines > 1024) {
        errorMsg = "Queue depths too large (max 1024 each)";
        return false;
    }
    
    if (config.sliceStrategy.empty()) {
        errorMsg = "Slice strategy cannot be empty";
        return false;
    }
    
    return true;
}

bool IQVideoStreamProcessor::validateConfig(const VideoProcessingConfig& config) {
    std::string errorMsg;
    if (!validateConfig(config, errorMsg)) {
        setError("Configuration validation failed: " + errorMsg);
        return false;
    }
    return true;
}



AcquisitionWorker::Config IQVideoStreamProcessor::toWorkerConfig(const VideoProcessingConfig& config) const {
    return AcquisitionWorker::Config::calculateWorkerConfig(config.queueDepth.raw);
}

void IQVideoStreamProcessor::workerThreadFunc() {
    try {
        // Initialize the acquisition worker
        if (!acquisitionWorker_->initialize()) {
            setError("Failed to initialize acquisition worker: " + acquisitionWorker_->lastError());
            workerStartupFailed_ = true;
            startupCondition_.notify_one();
            return;
        }

        // Signal that worker is ready
        {
            std::lock_guard<std::mutex> lock(startupMutex_);
            workerReady_ = true;
        }
        startupCondition_.notify_one();

        // Main processing loop
        while (!stopRequested_ && acquisitionWorker_->canContinue()) {
            if (!acquisitionWorker_->processIteration()) {
                break;
            }
        }

    } catch (const std::exception& e) {
        setError("Worker thread exception: " + std::string(e.what()));
        workerStartupFailed_ = true;
        startupCondition_.notify_one();
    }
}

void IQVideoStreamProcessor::handleProcessedChunk(SampleType* data, size_t size) {
    // For now, output to stdout as specified in strategy document
    // TODO: Implement actual video processing pipeline (stages 3-6)
    std::cout << "Processed chunk: " << size << " samples" << std::endl;

    // TODO: When video processing is implemented, call frameCallback_ with frame data
    // frameCallback_(frameData, frameSize);
}

void IQVideoStreamProcessor::setError(const std::string& error) {
    lastError_ = error;
}


