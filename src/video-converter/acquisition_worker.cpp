#include "acquisition_worker.h"
#include <algorithm>
#include <stdexcept>

AcquisitionWorker::AcquisitionWorker(std::unique_ptr<IIQStream> stream, const Config& config, ReadHandler readHandler)
    : config_(config), readHandler_(std::move(read<PERSON><PERSON><PERSON>)), stream_(std::move(stream)), initialized_(false) {
    stats_.startTime = std::chrono::steady_clock::now();
}

AcquisitionWorker::~AcquisitionWorker() {
    // Clean shutdown - no thread management here
}

bool AcquisitionWorker::initialize() {
    if (initialized_) {
        lastError_ = "Worker is already initialized";
        return false;
    }
    
    if (!stream_ || !stream_->isActive()) {
        lastError_ = "Stream is not active";
        return false;
    }
    
    // Calculate actual read chunk size if using auto strategy
    size_t actualReadChunkSize = config_.readChunkSize;
    if (config_.sliceStrategy == "auto_ntsc") {
        actualReadChunkSize = calculateSliceSize(config_.sampleRate, config_.sliceStrategy);
    }
    
    try {
        // Create chunk processor with calculated parameters
        chunkProcessor_ = std::make_unique<ChunkProcessor<SampleType>>(
            config_.writeChunkSize,
            actualReadChunkSize,
            config_.readOverlapSize,
            config_.numWriteChunks,
            [this](SampleType* data, size_t size) { onChunkReady(data, size); }
        );
        
        initialized_ = true;
        return true;
    } catch (const std::exception& e) {
        lastError_ = "Failed to initialize chunk processor: " + std::string(e.what());
        return false;
    }
}

bool AcquisitionWorker::processIteration() {
    if (!initialized_) {
        lastError_ = "Worker not initialized";
        return false;
    }
    
    if (!stream_->isActive()) {
        return false;  // End of stream
    }
    
    // Get write buffer from chunk processor
    SampleType* writePtr = chunkProcessor_->getWriteChunkPtr();
    
    // Read samples from stream
    bool success = stream_->readSamples(writePtr, config_.writeChunkSize);
    
    if (success) {
        stats_.totalSamplesRead += config_.writeChunkSize;
        // Commit the chunk - this may trigger read callbacks
        chunkProcessor_->commitWriteChunk();
        return true;
    } else {
        if (stream_->isActive()) {
            // Stream error (not EOS)
            stats_.streamErrors++;
            lastError_ = "Stream read error: " + stream_->lastError();
        }
        return false;
    }
}

bool AcquisitionWorker::canContinue() const {
    return initialized_ && stream_ && stream_->isActive();
}

const std::string& AcquisitionWorker::lastError() const {
    return lastError_;
}

const AcquisitionWorker::Stats& AcquisitionWorker::getStats() const {
    return stats_;
}

size_t AcquisitionWorker::calculateSliceSize(uint32_t sampleRate, const std::string& strategy) {
    if (strategy == "auto_ntsc") {
        // Calculate chunk size to contain 4 full NTSC video lines
        // NTSC line rate is approximately 15.734 kHz
        const double ntscLineRate = 15734.0;  // Hz
        const int linesPerChunk = 4;
        
        size_t samplesPerLine = static_cast<size_t>(sampleRate / ntscLineRate);
        return samplesPerLine * linesPerChunk;
    }
    
    // Default to configured read chunk size
    return config_.readChunkSize;
}

void AcquisitionWorker::onChunkReady(SampleType* data, size_t size) {
    stats_.totalChunksProcessed++;
    if (readHandler_) {
        readHandler_(data, size);
    }
}

AcquisitionWorker::Config AcquisitionWorker::Config::calculateWorkerConfig(uint32_t queueDepthRaw) {
    Config workerConfig;

    // Use conservative defaults to avoid assertion failures
    workerConfig.writeChunkSize = 1024;   // Smaller write chunk size
    workerConfig.readChunkSize = 1024;    // Same as write chunk size for simplicity
    workerConfig.readOverlapSize = 256;   // 25% overlap
    workerConfig.numWriteChunks = std::max(queueDepthRaw, static_cast<uint32_t>(4));
    workerConfig.sampleRate = 20000000;   // Default 20 MS/s (will be overridden)
    workerConfig.sliceStrategy = "fixed"; // Use fixed strategy to avoid auto calculation issues

    return workerConfig;
}
