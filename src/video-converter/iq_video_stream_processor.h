#pragma once

#include <memory>
#include <string>
#include <atomic>
#include <functional>
#include <thread>
#include <mutex>
#include <condition_variable>
#include "acquisition_worker.h"
#include "../iiq-stream/iiq_stream.h"
#include "../node/config_helpers.h"

/**
 * IQVideoStreamProcessor - IQ-to-video stream conversion orchestrator
 *
 * Manages the complete IQ-to-video demodulation process for a single stream.
 * Simplified single-instance design without global handle management.
 * Takes an initialized IIQStream and orchestrates video processing pipeline.
 */
class IQVideoStreamProcessor {
public:
    /**
     * Video processing configuration (Node.js agnostic)
     * Uses the configuration structure from ConfigHelpers
     */
    struct VideoProcessingConfig {
        // Video-specific configuration
        double centerOffsetHz;      // Frequency offset for demodulation
        std::string sliceStrategy;  // Slice strategy (e.g., "auto_ntsc")
        uint32_t frameWidthPx;      // Output frame width in pixels

        struct QueueDepth {
            uint32_t raw;           // Raw sample queue depth
            uint32_t demod;         // Demodulated data queue depth
            uint32_t lines;         // Video line queue depth
        } queueDepth;
    };
    
    /**
     * Frame callback function type
     * Called when a complete video frame is ready
     */
    using FrameCallback = std::function<void(const uint8_t* frameData, size_t frameSize)>;

    /**
     * Constructor
     * @param stream Initialized and validated IIQStream (takes ownership)
     * @param config Video processing configuration
     * @param frameCallback Callback for processed video frames
     */
    IQVideoStreamProcessor(std::unique_ptr<IIQStream> stream, const VideoProcessingConfig& config, const FrameCallback& frameCallback);
    ~IQVideoStreamProcessor();
    
    /**
     * Initialize and start video conversion with synchronous thread startup
     * Blocks until the worker thread is fully initialized and ready
     * @return true if successful, false otherwise
     */
    bool start();

    /**
     * Stop video conversion gracefully
     */
    void stop();
    
    /**
     * Check if converter is running
     * @return true if running, false otherwise
     */
    bool isRunning() const;
    
    /**
     * Get conversion statistics as JSON string
     * @return Statistics JSON or empty object on error
     */
    std::string getStats() const;
    
    /**
     * Get the last error message
     * @return Last error message or empty string if no error
     */
    const std::string& lastError() const;
    
    /**
     * Validate video processing configuration
     * @param config Configuration to validate
     * @param errorMsg Output parameter for error message
     * @return true if valid, false otherwise
     */
    static bool validateConfig(const VideoProcessingConfig& config, std::string& errorMsg);

private:
    // Configuration and callback
    VideoProcessingConfig config_;
    const FrameCallback& frameCallback_;

    // Processing components
    std::unique_ptr<IIQStream> stream_;
    std::unique_ptr<AcquisitionWorker> acquisitionWorker_;

    // Thread management
    std::unique_ptr<std::thread> workerThread_;
    std::mutex startupMutex_;
    std::condition_variable startupCondition_;
    std::atomic<bool> workerReady_;
    std::atomic<bool> workerStartupFailed_;

    // State management
    std::atomic<bool> running_;
    std::atomic<bool> stopRequested_;
    std::string lastError_;
    
    /**
     * Validate configuration parameters
     * @param config Configuration to validate
     * @return true if valid, false otherwise
     */
    bool validateConfig(const VideoProcessingConfig& config);
    
    /**
     * Convert VideoProcessingConfig to AcquisitionWorker::Config
     * @param config Video processing configuration
     * @return Worker configuration
     */
    AcquisitionWorker::Config toWorkerConfig(const VideoProcessingConfig& config) const;

    /**
     * Worker thread main function
     */
    void workerThreadFunc();

    /**
     * Handle processed chunks from acquisition worker
     * Currently outputs to stdout as specified in strategy
     * @param data Processed chunk data
     * @param size Size of chunk in samples
     */
    void handleProcessedChunk(SampleType* data, size_t size);

    /**
     * Set error message
     * @param error Error message to set
     */
    void setError(const std::string& error);
    
    // Disable copy and assignment
    IQVideoStreamProcessor(const IQVideoStreamProcessor&) = delete;
    IQVideoStreamProcessor& operator=(const IQVideoStreamProcessor&) = delete;
};
