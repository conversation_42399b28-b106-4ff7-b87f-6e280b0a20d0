#ifndef ACQUISITION_WORKER_H
#define ACQUISITION_WORKER_H

#include "../iiq-stream/iiq_stream.h"
#include "../chunk-processor/chunk_processor.h"
#include "../types.h"
#include <memory>
#include <atomic>
#include <functional>
#include <vector>
#include <cstdint>
#include <string>
#include <chrono>

/**
 * AcquisitionWorker - Stage 1-2 of the pipeline
 * 
 * Acquires IQ samples from an IIQStream source and slices them into chunks
 * using the ChunkProcessor. Contains only core acquisition logic without
 * thread management (threading is handled by IQVideoStreamProcessor).
 */
class AcquisitionWorker {
public:
    using ReadHandler = std::function<void(SampleType*, size_t)>;
    
    /**
     * Configuration for the acquisition worker
     */
    struct Config {
        size_t writeChunkSize;      // Size of each write chunk in samples
        size_t readChunkSize;       // Size of each read chunk in samples
        size_t readOverlapSize;     // Overlap between read chunks in samples
        size_t numWriteChunks;      // Number of write chunks in buffer
        uint32_t sampleRate;        // Sample rate in Hz
        std::string sliceStrategy;  // Slice strategy ("fixed", "auto_ntsc")
        
        /**
         * Calculate worker configuration from queue depth
         * @param queueDepthRaw Raw sample queue depth
         * @return Calculated worker configuration
         */
        static Config calculateWorkerConfig(uint32_t queueDepthRaw);
    };
    
    /**
     * Constructor
     * @param stream IQ data stream source
     * @param config Configuration parameters
     * @param readHandler Callback for processed chunks
     */
    AcquisitionWorker(std::unique_ptr<IIQStream> stream,
                      const Config& config,
                      ReadHandler readHandler);
    
    /**
     * Destructor
     */
    ~AcquisitionWorker();
    
    /**
     * Initialize the acquisition worker
     * @return true if initialized successfully, false otherwise
     */
    bool initialize();
    
    /**
     * Process one iteration of the acquisition loop
     * @return true if should continue, false if should stop
     */
    bool processIteration();
    
    /**
     * Check if the worker can continue processing
     */
    bool canContinue() const;
    
    /**
     * Get the last error message
     */
    const std::string& lastError() const;
    
    /**
     * Get processing statistics
     */
    struct Stats {
        uint64_t totalSamplesRead;
        uint64_t totalChunksProcessed;
        uint64_t streamErrors;
        std::chrono::steady_clock::time_point startTime;
        
        Stats() : totalSamplesRead(0), totalChunksProcessed(0), streamErrors(0) {}
    };
    
    const Stats& getStats() const;

private:
    // Configuration
    Config config_;
    ReadHandler readHandler_;
    
    // Stream and processing
    std::unique_ptr<IIQStream> stream_;
    std::unique_ptr<ChunkProcessor<SampleType>> chunkProcessor_;
    
    // State
    bool initialized_;
    
    // Error handling
    mutable std::string lastError_;
    
    // Statistics
    mutable Stats stats_;
    
    /**
     * Calculate read chunk size based on slice strategy
     * @param sampleRate Sample rate in Hz
     * @param strategy Slice strategy string
     * @return Calculated chunk size in samples
     */
    size_t calculateSliceSize(uint32_t sampleRate, const std::string& strategy);
    
    /**
     * Chunk processor callback - forwards to read handler
     */
    void onChunkReady(SampleType* data, size_t size);
    
    // Disable copy constructor and assignment
    AcquisitionWorker(const AcquisitionWorker&) = delete;
    AcquisitionWorker& operator=(const AcquisitionWorker&) = delete;
};

#endif // ACQUISITION_WORKER_H
