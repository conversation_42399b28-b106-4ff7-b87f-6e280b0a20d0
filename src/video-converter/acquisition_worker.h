#ifndef ACQUISITION_WORKER_H
#define ACQUISITION_WORKER_H

#include "../iiq-stream/iiq_stream.h"
#include "../chunk-processor/chunk_processor.h"
#include <memory>
#include <thread>
#include <atomic>
#include <functional>
#include <vector>
#include <cstdint>

/**
 * AcquisitionWorker - Stage 1-2 of the pipeline
 * 
 * Acquires IQ samples from an IIQStream source and slices them into chunks
 * using the ChunkProcessor. Runs in its own thread and pushes data to
 * downstream processing stages.
 */
template<typename SampleType = uint32_t>
class AcquisitionWorker {
public:
    using ReadHandler = std::function<void(SampleType*, size_t)>;

    /**
     * Base configuration structure for acquisition worker
     * Contains only the core parameters needed by AcquisitionWorker
     */
    struct BaseConfig {
        size_t writeChunkSize;      // Size of write chunks from stream
        size_t readChunkSize;       // Size of read chunks for processing (8K samples)
        size_t readOverlapSize;     // Overlap between read chunks
        size_t numWriteChunks;      // Number of write chunks in buffer
        uint32_t sampleRate;        // Sample rate for slice calculations
        std::string sliceStrategy;  // Strategy for slice size calculation
    };

    /**
     * Configuration structure for the acquisition worker
     * (Alias for BaseConfig for backward compatibility)
     */
    using Config = BaseConfig;
    
    /**
     * Constructor
     * @param stream IQ data stream source
     * @param config Configuration parameters
     * @param readHandler Callback for processed chunks
     */
    AcquisitionWorker(std::unique_ptr<IIQStream> stream,
                      const Config& config,
                      ReadHandler readHandler);
    
    /**
     * Destructor
     */
    ~AcquisitionWorker();
    
    /**
     * Start the acquisition worker thread
     * @return true if started successfully, false otherwise
     */
    bool start();
    
    /**
     * Request the worker to stop gracefully
     */
    void requestStop();
    
    /**
     * Wait for the worker thread to finish
     */
    void join();
    
    /**
     * Check if the worker is currently running
     */
    bool isRunning() const;
    
    /**
     * Get the last error message
     */
    const std::string& lastError() const;
    
    /**
     * Get statistics about the acquisition process
     */
    struct Stats {
        uint64_t totalSamplesRead;
        uint64_t totalChunksProcessed;
        uint64_t streamErrors;
        double averageReadRate;  // samples per second
    };
    
    Stats getStats() const;

    /**
     * Calculate worker configuration from video processing parameters
     * @param queueDepthRaw Raw sample queue depth from video config
     * @return Worker configuration for AcquisitionWorker
     */
    static Config calculateWorkerConfig(uint32_t queueDepthRaw);

private:
    // Configuration
    Config config_;
    ReadHandler readHandler_;
    
    // Stream and processing
    std::unique_ptr<IIQStream> stream_;
    std::unique_ptr<ChunkProcessor<SampleType>> chunkProcessor_;
    
    // Threading
    std::unique_ptr<std::thread> workerThread_;
    std::atomic<bool> running_;
    std::atomic<bool> stopRequested_;
    
    // Error handling
    mutable std::string lastError_;
    
    // Statistics
    mutable std::atomic<uint64_t> totalSamplesRead_;
    mutable std::atomic<uint64_t> totalChunksProcessed_;
    mutable std::atomic<uint64_t> streamErrors_;
    mutable std::chrono::steady_clock::time_point startTime_;
    
    /**
     * Main worker thread function
     */
    void workerThreadFunc();
    
    /**
     * Calculate read chunk size based on slice strategy
     * @param sampleRate Sample rate in Hz
     * @param strategy Slice strategy string
     * @return Calculated chunk size in samples
     */
    size_t calculateSliceSize(uint32_t sampleRate, const std::string& strategy);
    
    /**
     * Chunk processor callback - forwards to read handler
     */
    void onChunkReady(SampleType* data, size_t size);
    
    // Disable copy constructor and assignment
    AcquisitionWorker(const AcquisitionWorker&) = delete;
    AcquisitionWorker& operator=(const AcquisitionWorker&) = delete;
};

// Template implementation
template<typename SampleType>
AcquisitionWorker<SampleType>::AcquisitionWorker(std::unique_ptr<IIQStream> stream, const Config& config, ReadHandler readHandler)
    : config_(config), readHandler_(std::move(readHandler)), stream_(std::move(stream)),
      running_(false), stopRequested_(false), totalSamplesRead_(0), 
      totalChunksProcessed_(0), streamErrors_(0) {
    
    // Calculate actual read chunk size if using auto strategy
    size_t actualReadChunkSize = config_.readChunkSize;
    if (config_.sliceStrategy == "auto_ntsc") {
        actualReadChunkSize = calculateSliceSize(config_.sampleRate, config_.sliceStrategy);
    }
    
    // Create chunk processor with calculated parameters
    chunkProcessor_ = std::make_unique<ChunkProcessor<SampleType>>(
        config_.writeChunkSize,
        actualReadChunkSize,
        config_.readOverlapSize,
        config_.numWriteChunks,
        [this](SampleType* data, size_t size) { onChunkReady(data, size); }
    );
}

template<typename SampleType>
AcquisitionWorker<SampleType>::~AcquisitionWorker() {
    if (running_) {
        requestStop();
        join();
    }
}

template<typename SampleType>
bool AcquisitionWorker<SampleType>::start() {
    if (running_) {
        lastError_ = "Worker is already running";
        return false;
    }
    
    if (!stream_ || !stream_->isActive()) {
        lastError_ = "Stream is not active";
        return false;
    }
    
    stopRequested_ = false;
    startTime_ = std::chrono::steady_clock::now();
    
    try {
        workerThread_ = std::make_unique<std::thread>(&AcquisitionWorker::workerThreadFunc, this);
        running_ = true;
        return true;
    } catch (const std::exception& e) {
        lastError_ = "Failed to start worker thread: " + std::string(e.what());
        return false;
    }
}

template<typename SampleType>
void AcquisitionWorker<SampleType>::requestStop() {
    stopRequested_ = true;
}

template<typename SampleType>
void AcquisitionWorker<SampleType>::join() {
    if (workerThread_ && workerThread_->joinable()) {
        workerThread_->join();
        workerThread_.reset();
        running_ = false;
    }
}

template<typename SampleType>
bool AcquisitionWorker<SampleType>::isRunning() const {
    return running_;
}

template<typename SampleType>
const std::string& AcquisitionWorker<SampleType>::lastError() const {
    return lastError_;
}

template<typename SampleType>
typename AcquisitionWorker<SampleType>::Stats AcquisitionWorker<SampleType>::getStats() const {
    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - startTime_).count();
    double elapsedSeconds = elapsed / 1000.0;
    
    Stats stats;
    stats.totalSamplesRead = totalSamplesRead_;
    stats.totalChunksProcessed = totalChunksProcessed_;
    stats.streamErrors = streamErrors_;
    stats.averageReadRate = elapsedSeconds > 0 ? stats.totalSamplesRead / elapsedSeconds : 0.0;
    
    return stats;
}

template<typename SampleType>
void AcquisitionWorker<SampleType>::workerThreadFunc() {
    while (!stopRequested_ && stream_->isActive()) {
        // Get write buffer from chunk processor
        SampleType* writePtr = chunkProcessor_->getWriteChunkPtr();
        
        // Read samples from stream
        bool success = stream_->readSamples(writePtr, config_.writeChunkSize);
        
        if (success) {
            totalSamplesRead_ += config_.writeChunkSize;
            // Commit the chunk - this may trigger read callbacks
            chunkProcessor_->commitWriteChunk();
        } else {
            if (stream_->isActive()) {
                // Stream error (not EOS)
                streamErrors_++;
                lastError_ = "Stream read error: " + stream_->lastError();
            }
            break;
        }
    }
    
    // Close stream when done
    stream_->close();
    running_ = false;
}

template<typename SampleType>
size_t AcquisitionWorker<SampleType>::calculateSliceSize(uint32_t sampleRate, const std::string& strategy) {
    if (strategy == "auto_ntsc") {
        // Calculate chunk size to contain 4 full NTSC video lines
        // NTSC line rate is approximately 15.734 kHz
        const double ntscLineRate = 15734.0;  // Hz
        const int linesPerChunk = 4;
        
        size_t samplesPerLine = static_cast<size_t>(sampleRate / ntscLineRate);
        return samplesPerLine * linesPerChunk;
    }
    
    // Default to configured read chunk size
    return config_.readChunkSize;
}

template<typename SampleType>
void AcquisitionWorker<SampleType>::onChunkReady(SampleType* data, size_t size) {
    totalChunksProcessed_++;
    if (readHandler_) {
        readHandler_(data, size);
    }
}

template<typename SampleType>
typename AcquisitionWorker<SampleType>::Config AcquisitionWorker<SampleType>::calculateWorkerConfig(uint32_t queueDepthRaw) {
    Config workerConfig;

    // Use conservative defaults to avoid assertion failures
    workerConfig.writeChunkSize = 1024;   // Smaller write chunk size
    workerConfig.readChunkSize = 1024;    // Same as write chunk size for simplicity
    workerConfig.readOverlapSize = 256;   // 25% overlap
    workerConfig.numWriteChunks = std::max(queueDepthRaw, static_cast<uint32_t>(4));
    workerConfig.sampleRate = 20000000;   // Default 20 MS/s (will be overridden)
    workerConfig.sliceStrategy = "fixed"; // Use fixed strategy to avoid auto calculation issues

    // Validate buffer size assertion: writeChunkSize * numWriteChunks >= readChunkSize + readOverlapSize
    size_t bufferSize = workerConfig.writeChunkSize * workerConfig.numWriteChunks;
    size_t requiredSize = workerConfig.readChunkSize + workerConfig.readOverlapSize;

    if (bufferSize < requiredSize) {
        // Increase numWriteChunks to satisfy the assertion
        workerConfig.numWriteChunks = (requiredSize + workerConfig.writeChunkSize - 1) / workerConfig.writeChunkSize;
        if (workerConfig.numWriteChunks < 2) {
            workerConfig.numWriteChunks = 2;  // Minimum required by ChunkProcessor
        }
    }

    return workerConfig;
}

#endif // ACQUISITION_WORKER_H
