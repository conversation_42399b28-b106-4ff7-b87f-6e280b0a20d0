#include "config_helpers.h"

namespace ConfigHelpers {

IQVideoStreamProcessor::VideoProcessingConfig parseVideoProcessingConfig(v8::Isolate* isolate, v8::Local<v8::Object> processingObj) {
    IQVideoStreamProcessor::VideoProcessingConfig config;
    v8::Local<v8::Context> context = isolate->GetCurrentContext();

    // Center offset
    v8::Local<v8::Value> centerOffsetVal = processingObj->Get(context, v8::String::NewFromUtf8(isolate, "centerOffsetHz").ToLocalChecked()).ToLocalChecked();
    if (centerOffsetVal->IsNumber()) {
        config.centerOffsetHz = centerOffsetVal->NumberValue(context).FromJust();
    }

    // Slice strategy
    v8::Local<v8::Value> sliceStrategyVal = processingObj->Get(context, v8::String::NewFromUtf8(isolate, "sliceStrategy").ToLocalChecked()).ToLocalChecked();
    if (sliceStrategyVal->IsString()) {
        v8::String::Utf8Value sliceStrategyStr(isolate, sliceStrategyVal);
        config.sliceStrategy = std::string(*sliceStrategyStr);
    } else {
        config.sliceStrategy = "auto_ntsc";  // default
    }

    // Frame width
    v8::Local<v8::Value> frameWidthVal = processingObj->Get(context, v8::String::NewFromUtf8(isolate, "frameWidthPx").ToLocalChecked()).ToLocalChecked();
    if (frameWidthVal->IsNumber()) {
        config.frameWidthPx = frameWidthVal->Uint32Value(context).FromJust();
    } else {
        config.frameWidthPx = 640;  // default
    }

    // Queue depths
    v8::Local<v8::Value> queueDepthVal = processingObj->Get(context, v8::String::NewFromUtf8(isolate, "queueDepth").ToLocalChecked()).ToLocalChecked();
    if (queueDepthVal->IsObject()) {
        v8::Local<v8::Object> queueDepthObj = queueDepthVal.As<v8::Object>();
        
        v8::Local<v8::Value> rawVal = queueDepthObj->Get(context, v8::String::NewFromUtf8(isolate, "raw").ToLocalChecked()).ToLocalChecked();
        if (rawVal->IsNumber()) {
            config.queueDepth.raw = rawVal->Uint32Value(context).FromJust();
        } else {
            config.queueDepth.raw = 128;  // default
        }
        
        v8::Local<v8::Value> demodVal = queueDepthObj->Get(context, v8::String::NewFromUtf8(isolate, "demod").ToLocalChecked()).ToLocalChecked();
        if (demodVal->IsNumber()) {
            config.queueDepth.demod = demodVal->Uint32Value(context).FromJust();
        } else {
            config.queueDepth.demod = 128;  // default
        }
        
        v8::Local<v8::Value> linesVal = queueDepthObj->Get(context, v8::String::NewFromUtf8(isolate, "lines").ToLocalChecked()).ToLocalChecked();
        if (linesVal->IsNumber()) {
            config.queueDepth.lines = linesVal->Uint32Value(context).FromJust();
        } else {
            config.queueDepth.lines = 64;  // default
        }
    } else {
        // Default queue depths
        config.queueDepth.raw = 128;
        config.queueDepth.demod = 128;
        config.queueDepth.lines = 64;
    }

    return config;
}

} // namespace ConfigHelpers
