#pragma once

#include <v8.h>
#include "../video-converter/iq_video_stream_processor.h"

namespace ConfigHelpers {

/**
 * Parse video processing configuration from JavaScript object
 * @param isolate V8 isolate
 * @param processingObj JavaScript object containing configuration
 * @return Parsed VideoProcessingConfig structure
 */
IQVideoStreamProcessor::VideoProcessingConfig parseVideoProcessingConfig(v8::Isolate* isolate, v8::Local<v8::Object> processingObj);

} // namespace ConfigHelpers
