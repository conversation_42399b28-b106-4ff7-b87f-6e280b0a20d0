#ifndef PIPELINE_MANAGER_H
#define PIPELINE_MANAGER_H

#include "../video-converter/acquisition_worker.h"
#include "../iiq-stream/iiq_stream.h"
#include "../wav-stream/wav_stream.h"
#include "../bladerf-stream/bladerf_stream.h"
#include <memory>
#include <string>
#include <atomic>
#include <functional>
#include <iostream>

/**
 * PipelineManager - Main pipeline orchestrator
 * 
 * Manages the complete FM-IQ video demodulation pipeline according to the strategy document.
 * Handles initialization, threading, and graceful shutdown of all pipeline stages.
 */
class PipelineManager {
public:
    /**
     * Source configuration for different input types
     */
    struct SourceConfig {
        std::string type;  // "wav" or "bladeRF"
        
        // WAV-specific parameters
        std::string path;
        std::string playMode;  // "realtime" or "max"
        
        // BladeRF-specific parameters
        std::string serial;
        uint32_t channel;
        uint32_t sampleRate;
        double centerHz;
        double bandwidth;
    };
    
    /**
     * Processing configuration
     */
    struct ProcessingConfig {
        double centerOffsetHz;
        std::string sliceStrategy;
        uint32_t frameWidthPx;
        
        struct QueueDepth {
            uint32_t raw;
            uint32_t demod;
            uint32_t lines;
        } queueDepth;
    };
    
    /**
     * Complete pipeline configuration
     */
    struct Config {
        SourceConfig source;
        ProcessingConfig processing;
    };
    
    /**
     * Pipeline handle for JavaScript interface
     */
    class Handle {
    public:
        Handle(std::shared_ptr<PipelineManager> manager);
        ~Handle();
        
        /**
         * Stop the pipeline gracefully
         */
        void stop();
        
        /**
         * Check if pipeline is running
         */
        bool isRunning() const;
        
        /**
         * Get pipeline statistics
         */
        std::string getStats() const;

    private:
        std::shared_ptr<PipelineManager> manager_;
    };
    
    /**
     * Frame callback function type
     */
    using FrameCallback = std::function<void(const uint8_t* frameData, size_t frameSize)>;
    
    /**
     * Start the pipeline with given configuration
     * @param config Pipeline configuration
     * @param frameCallback Callback for processed frames
     * @return Pipeline handle or nullptr on failure
     */
    static std::unique_ptr<Handle> startPipeline(const Config& config, FrameCallback frameCallback);
    
    /**
     * Constructor (private - use startPipeline)
     */
    PipelineManager(const Config& config, FrameCallback frameCallback);
    
    /**
     * Destructor
     */
    ~PipelineManager();
    
    /**
     * Initialize and start the pipeline
     * @return true if successful, false otherwise
     */
    bool initialize();
    
    /**
     * Stop the pipeline gracefully
     */
    void stop();
    
    /**
     * Check if pipeline is running
     */
    bool isRunning() const;
    
    /**
     * Get the last error message
     */
    const std::string& lastError() const;
    
    /**
     * Get pipeline statistics as JSON string
     */
    std::string getStatsJson() const;

private:
    // Configuration
    Config config_;
    FrameCallback frameCallback_;
    
    // Pipeline components
    std::unique_ptr<IIQStream> stream_;
    std::unique_ptr<AcquisitionWorker<uint32_t>> acquisitionWorker_;
    
    // State management
    std::atomic<bool> running_;
    std::atomic<bool> stopRequested_;
    std::string lastError_;
    
    /**
     * Create IQ stream based on source configuration
     */
    std::unique_ptr<IIQStream> createStream(const SourceConfig& sourceConfig);
    
    /**
     * Validate configuration parameters
     */
    bool validateConfig(const Config& config);
    
    /**
     * Handle processed chunks from acquisition worker
     * For now, this will output to stdout as specified
     */
    void handleProcessedChunk(uint32_t* data, size_t size);
    
    /**
     * Calculate optimal buffer sizes based on configuration
     */
    AcquisitionWorker<uint32_t>::Config calculateWorkerConfig(const Config& config);
    
    // Disable copy constructor and assignment
    PipelineManager(const PipelineManager&) = delete;
    PipelineManager& operator=(const PipelineManager&) = delete;
};

#endif // PIPELINE_MANAGER_H
