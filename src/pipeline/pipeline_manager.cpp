#include "pipeline_manager.h"
#include <stdexcept>
#include <sstream>
#include <iomanip>

// Handle implementation
PipelineManager::Handle::Handle(std::shared_ptr<PipelineManager> manager)
    : manager_(std::move(manager)) {
}

PipelineManager::Handle::~Handle() {
    if (manager_) {
        manager_->stop();
    }
}

void PipelineManager::Handle::stop() {
    if (manager_) {
        manager_->stop();
    }
}

bool PipelineManager::Handle::isRunning() const {
    return manager_ && manager_->isRunning();
}

std::string PipelineManager::Handle::getStats() const {
    return manager_ ? manager_->getStatsJson() : "{}";
}

// PipelineManager implementation
std::unique_ptr<PipelineManager::Handle> PipelineManager::startPipeline(
    const Config& config, FrameCallback frameCallback) {
    
    auto manager = std::shared_ptr<PipelineManager>(new PipelineManager(config, frameCallback));
    
    if (!manager->initialize()) {
        return nullptr;
    }
    
    return std::make_unique<Handle>(manager);
}

PipelineManager::PipelineManager(const Config& config, FrameCallback frameCallback)
    : config_(config), frameCallback_(frameCallback), running_(false), stopRequested_(false) {
}

PipelineManager::~PipelineManager() {
    stop();
}

bool PipelineManager::initialize() {
    try {
        // Validate configuration
        if (!validateConfig(config_)) {
            return false;
        }
        
        // Create IQ stream
        stream_ = createStream(config_.source);
        if (!stream_) {
            lastError_ = "Failed to create IQ stream";
            return false;
        }
        
        // Calculate worker configuration
        auto workerConfig = calculateWorkerConfig(config_);
        
        // Create acquisition worker
        acquisitionWorker_ = std::make_unique<AcquisitionWorker<uint32_t>>(
            std::move(stream_),
            workerConfig,
            [this](uint32_t* data, size_t size) { handleProcessedChunk(data, size); }
        );
        
        // Start the acquisition worker
        if (!acquisitionWorker_->start()) {
            lastError_ = "Failed to start acquisition worker: " + acquisitionWorker_->lastError();
            return false;
        }
        
        running_ = true;
        return true;
        
    } catch (const std::exception& e) {
        lastError_ = "Initialization failed: " + std::string(e.what());
        return false;
    }
}

void PipelineManager::stop() {
    if (running_.exchange(false)) {
        stopRequested_ = true;
        
        if (acquisitionWorker_) {
            acquisitionWorker_->requestStop();
            acquisitionWorker_->join();
            acquisitionWorker_.reset();
        }
        
        stream_.reset();
    }
}

bool PipelineManager::isRunning() const {
    return running_ && acquisitionWorker_ && acquisitionWorker_->isRunning();
}

const std::string& PipelineManager::lastError() const {
    return lastError_;
}

std::string PipelineManager::getStatsJson() const {
    std::ostringstream oss;
    oss << "{";
    
    if (acquisitionWorker_) {
        auto stats = acquisitionWorker_->getStats();
        oss << "\"totalSamplesRead\":" << stats.totalSamplesRead << ",";
        oss << "\"totalChunksProcessed\":" << stats.totalChunksProcessed << ",";
        oss << "\"streamErrors\":" << stats.streamErrors << ",";
        oss << "\"averageReadRate\":" << std::fixed << std::setprecision(2) << stats.averageReadRate;
    }
    
    oss << ",\"running\":" << (isRunning() ? "true" : "false");
    oss << "}";
    
    return oss.str();
}

std::unique_ptr<IIQStream> PipelineManager::createStream(const SourceConfig& sourceConfig) {
    try {
        if (sourceConfig.type == "wav") {
            bool realtime = (sourceConfig.playMode == "realtime");
            return std::make_unique<WavStream>(sourceConfig.path, realtime);
            
        } else if (sourceConfig.type == "bladeRF") {
            return std::make_unique<BladeRFStream>(
                sourceConfig.serial,
                sourceConfig.channel,
                sourceConfig.sampleRate,
                sourceConfig.centerHz,
                sourceConfig.bandwidth
            );
            
        } else {
            lastError_ = "Unknown source type: " + sourceConfig.type;
            return nullptr;
        }
        
    } catch (const std::exception& e) {
        lastError_ = "Failed to create " + sourceConfig.type + " stream: " + e.what();
        return nullptr;
    }
}

bool PipelineManager::validateConfig(const Config& config) {
    // Validate source configuration
    if (config.source.type != "wav" && config.source.type != "bladeRF") {
        lastError_ = "Invalid source type: " + config.source.type;
        return false;
    }
    
    if (config.source.type == "wav") {
        if (config.source.path.empty()) {
            lastError_ = "WAV path cannot be empty";
            return false;
        }
        if (config.source.playMode != "realtime" && config.source.playMode != "max") {
            lastError_ = "Invalid WAV play mode: " + config.source.playMode;
            return false;
        }
    }
    
    if (config.source.type == "bladeRF") {
        if (config.source.sampleRate == 0) {
            lastError_ = "BladeRF sample rate cannot be zero";
            return false;
        }
        if (config.source.centerHz <= 0) {
            lastError_ = "BladeRF center frequency must be positive";
            return false;
        }
        if (config.source.bandwidth <= 0) {
            lastError_ = "BladeRF bandwidth must be positive";
            return false;
        }
    }
    
    // Validate processing configuration
    if (config.processing.frameWidthPx == 0) {
        lastError_ = "Frame width cannot be zero";
        return false;
    }
    
    if (config.processing.queueDepth.raw == 0 || 
        config.processing.queueDepth.demod == 0 || 
        config.processing.queueDepth.lines == 0) {
        lastError_ = "Queue depths must be positive";
        return false;
    }
    
    return true;
}

void PipelineManager::handleProcessedChunk(uint32_t* data, size_t size) {
    // For this iteration: simple stdout output as specified
    std::cout << "Processed chunk: " << size << " samples" << std::endl;
    
    // In future iterations, this will:
    // 1. Pass data to demodulation stage
    // 2. Handle sync detection
    // 3. Normalize and aggregate into frames
    // 4. Call frameCallback_ with processed frame data
    
    // For now, just demonstrate we're receiving data
    if (size > 0) {
        std::cout << "  First sample: 0x" << std::hex << data[0] << std::dec << std::endl;
        if (size > 1) {
            std::cout << "  Last sample:  0x" << std::hex << data[size-1] << std::dec << std::endl;
        }
    }
}

AcquisitionWorker<uint32_t>::Config PipelineManager::calculateWorkerConfig(const Config& config) {
    AcquisitionWorker<uint32_t>::Config workerConfig;
    
    // Use 8K samples as specified in strategy
    workerConfig.readChunkSize = 8192;
    
    // Write chunk size should be smaller for efficient streaming
    workerConfig.writeChunkSize = 1024;
    
    // Calculate overlap for 4 NTSC lines as specified
    // Overlap should be enough to ensure continuity
    workerConfig.readOverlapSize = workerConfig.readChunkSize / 8;  // 12.5% overlap
    
    // Buffer depth from configuration
    workerConfig.numWriteChunks = config.processing.queueDepth.raw;
    
    // Sample rate from source
    if (config.source.type == "bladeRF") {
        workerConfig.sampleRate = config.source.sampleRate;
    } else {
        // For WAV files, we'll get the actual rate from the stream
        workerConfig.sampleRate = 20000000;  // Default 20 MS/s as per strategy
    }
    
    workerConfig.sliceStrategy = config.processing.sliceStrategy;
    
    return workerConfig;
}
