#include "simple_pipeline.h"
#include "../wav-stream/wav_stream.h"
#include "../bladerf-stream/bladerf_stream.h"
#include <stdexcept>

SimplePipeline::SimplePipeline(const Config& config)
    : config_(config), running_(false) {
}

SimplePipeline::~SimplePipeline() {
    stop();
}

bool SimplePipeline::initialize() {
    try {
        stream_ = createStream();
        if (!stream_) {
            lastError_ = "Failed to create stream";
            return false;
        }
        
        if (!stream_->isActive()) {
            lastError_ = "Stream is not active: " + stream_->lastError();
            return false;
        }
        
        return true;
        
    } catch (const std::exception& e) {
        lastError_ = "Initialization failed: " + std::string(e.what());
        return false;
    }
}

bool SimplePipeline::start() {
    if (!stream_) {
        lastError_ = "Pipeline not initialized";
        return false;
    }
    
    running_ = true;
    return true;
}

void SimplePipeline::stop() {
    running_ = false;
    if (stream_) {
        stream_->close();
    }
}

bool SimplePipeline::isRunning() const {
    return running_;
}

const std::string& SimplePipeline::lastError() const {
    return lastError_;
}

std::unique_ptr<IIQStream> SimplePipeline::createStream() {
    try {
        if (config_.sourceType == "wav") {
            bool realtime = (config_.playMode == "realtime");
            return std::make_unique<WavStream>(config_.sourcePath, realtime);
            
        } else if (config_.sourceType == "bladerf") {
            return std::make_unique<BladeRFStream>(
                "",  // serial
                0,   // channel
                config_.sampleRate,
                config_.centerHz,
                config_.bandwidth
            );
            
        } else {
            lastError_ = "Unknown source type: " + config_.sourceType;
            return nullptr;
        }
        
    } catch (const std::exception& e) {
        lastError_ = "Failed to create " + config_.sourceType + " stream: " + e.what();
        return nullptr;
    }
}
