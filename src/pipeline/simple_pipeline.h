#ifndef SIMPLE_PIPELINE_H
#define SIMPLE_PIPELINE_H

#include "../iiq-stream/iiq_stream.h"
#include <memory>
#include <string>
#include <atomic>

/**
 * SimplePipeline - Minimal pipeline implementation for testing
 * 
 * This is a simplified version without templates to isolate potential issues
 */
class SimplePipeline {
public:
    struct Config {
        std::string sourceType;
        std::string sourcePath;
        std::string playMode;
        uint32_t sampleRate;
        double centerHz;
        double bandwidth;
    };
    
    /**
     * Constructor
     */
    SimplePipeline(const Config& config);
    
    /**
     * Destructor
     */
    ~SimplePipeline();
    
    /**
     * Initialize the pipeline
     */
    bool initialize();
    
    /**
     * Start processing
     */
    bool start();
    
    /**
     * Stop processing
     */
    void stop();
    
    /**
     * Check if running
     */
    bool isRunning() const;
    
    /**
     * Get last error
     */
    const std::string& lastError() const;

private:
    Config config_;
    std::unique_ptr<IIQStream> stream_;
    std::atomic<bool> running_;
    std::string lastError_;
    
    /**
     * Create stream based on configuration
     */
    std::unique_ptr<IIQStream> createStream();
};

#endif // SIMPLE_PIPELINE_H
