#ifndef WAV_STREAM_H
#define WAV_STREAM_H

#include "../iiq-stream/iiq_stream.h"
#include <string>
#include <chrono>

// Include dr_wav header
#include "dr_wav.h"

/**
 * WavStream - IQ data streaming from WAV files
 * 
 * Reads 16-bit stereo WAV files where left channel is I and right channel is Q.
 * Supports realtime playback pacing to simulate live streaming.
 */
class WavStream final : public IIQStream {
public:
    /**
     * Constructor
     * @param path Path to WAV file
     * @param realtime Enable realtime pacing (default: true)
     */
    WavStream(const std::string& path, bool realtime = true);
    
    /**
     * Destructor
     */
    ~WavStream() override;
    
    // IIQStream interface implementation
    bool readSamples(uint32_t* dst, size_t sampleCount) override;
    uint32_t sampleRate() const noexcept override;
    const std::string& sourceName() const noexcept override;
    bool isActive() const noexcept override;
    void close() noexcept override;
    const std::string& lastError() const noexcept override;

private:
    // WAV file handling
    drwav* wav_;
    uint32_t fs_;
    bool active_;
    std::string err_;
    
    // Realtime pacing
    uint64_t paceNs_;
    std::chrono::steady_clock::time_point lastReadTime_;
    
    // Source name constant
    static const std::string sourceName_;
    
    /**
     * Sleep for specified nanoseconds using busy/yield approach
     * @param nanoseconds Duration to sleep
     */
    void spinSleep(uint64_t nanoseconds);
    
    // Disable copy constructor and assignment
    WavStream(const WavStream&) = delete;
    WavStream& operator=(const WavStream&) = delete;
};

#endif // WAV_STREAM_H
