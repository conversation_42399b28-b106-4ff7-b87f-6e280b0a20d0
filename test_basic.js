#!/usr/bin/env node

const addon = require('./build/Release/bladerf_addon');

console.log('Testing basic addon functionality...');

try {
    console.log('1. Hello World:', addon.helloWorld());
    
    console.log('2. Testing simple video converter start...');

    const config = {
        source: {
            type: 'wav',
            path: 'samples/test_long.wav',
            playMode: 'max'
        },
        processing: {
            centerOffsetHz: 0,
            sliceStrategy: 'auto_ntsc',
            frameWidthPx: 640,
            queueDepth: {
                raw: 128,
                demod: 128,
                lines: 64
            }
        }
    };

    const frameCallback = () => {
        console.log('Frame callback called');
    };

    console.log('About to call createVideoConverter...');
    const result = addon.createVideoConverter(config, frameCallback);
    console.log('createVideoConverter returned:', result);

    if (result && result.success) {
        console.log('Video converter started successfully');

        // Stop immediately
        setTimeout(() => {
            console.log('Stopping video converter...');
            addon.stopVideoConverter();
            console.log('Video converter stopped');
        }, 100);
    }
    
} catch (error) {
    console.error('Error:', error.message);
    console.error('Stack:', error.stack);
}
