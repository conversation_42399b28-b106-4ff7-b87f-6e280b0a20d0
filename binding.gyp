{"targets": [{"target_name": "bladerf_addon", "sources": ["src/addon.cpp", "src/wav-stream/wav_stream.cpp", "src/bladerf-stream/bladerf_stream.cpp", "src/stream-factory/iq_stream_factory.cpp", "src/video-converter/iq_video_stream_processor.cpp", "src/pipeline/pipeline_manager.cpp", "src/pipeline/simple_pipeline.cpp"], "include_dirs": ["/usr/local/include", "/usr/include", "/opt/homebrew/include", "/opt/homebrew/Cellar/libbladerf/2023.02_1/include", "src/chunk-processor", "src/iiq-stream", "src/wav-stream", "src/bladerf-stream", "src/stream-factory", "src/video-converter", "src/pipeline"], "conditions": [["OS=='mac'", {"xcode_settings": {"GCC_ENABLE_CPP_EXCEPTIONS": "YES", "CLANG_CXX_LANGUAGE_STANDARD": "c++17", "MACOSX_DEPLOYMENT_TARGET": "10.9"}}], ["OS=='linux'", {"cflags_cc": ["-std=c++17"], "conditions": [["target_arch=='arm64'", {"cflags_cc": ["-std=c++17", "-mcpu=cortex-a76", "-mtune=cortex-a76", "-O3", "-ffast-math", "-funroll-loops"], "cflags": ["-mcpu=cortex-a76", "-mtune=cortex-a76", "-O3", "-ffast-math", "-funroll-loops"], "ldflags": ["-mcpu=cortex-a76"]}], ["target_arch=='arm'", {"cflags_cc": ["-std=c++17", "-mcpu=cortex-a53", "-mtune=cortex-a53", "-mfpu=neon-fp-armv8", "-mfloat-abi=hard", "-O3", "-ffast-math"], "cflags": ["-mcpu=cortex-a53", "-mtune=cortex-a53", "-mfpu=neon-fp-armv8", "-mfloat-abi=hard", "-O3", "-ffast-math"], "ldflags": ["-mcpu=cortex-a53", "-mfloat-abi=hard"]}]]}]], "libraries": ["-lbladeRF"], "library_dirs": ["/usr/local/lib", "/usr/lib", "/opt/homebrew/lib", "/opt/homebrew/Cellar/libbladerf/2023.02_1/lib"], "cflags_cc": ["-std=c++17", "-fexceptions", "-Wall", "-Wextra"], "cflags_cc!": ["-fno-exceptions"]}]}